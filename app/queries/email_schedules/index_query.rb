# frozen_string_literal: true

# typed: true

module EmailSchedules
  class IndexQuery
    extend T::Sig

    def initialize(tenant_id, source_type: nil, source_id: nil, dest_type: nil, before: nil, after: nil, limit: nil)
      @tenant_id = tenant_id.to_i
      @source_type = source_type
      @source_id = source_id
      @dest_type = dest_type
      @before = before
      @after = after
      @limit = limit
    end

    def execute
      list = build_relation
      list = select_list(list)
      list = T.cast(paginator.paginate(list, before: @before, after: @after, limit: @limit), T::Array[EmailSchedule])
      preloader = ActiveRecord::Associations::Preloader.new
      preloader.preload(
        list,
        [
          :schedule,
          :dest,
          :creator,
          :dynamic_filter_presets,
          :source,
        ],
      )

      _, non_canvas_ds = list.partition { |ds| ds.source.is_a?(Dashboard) && ds.source.is_v4? }

      if non_canvas_ds.present?
        preloader.preload(
          non_canvas_ds,
          [
            {
              source: :filter_ownerships,
              filter_values: :filter_ownership,
              dynamic_filter_presets: { dynamic_filter: :dynamic_filter_definition },
            },
          ],
        )
      end

      data_source_dests = list.each_with_object([]) do |es, dests|
        dests << es.dest if %w[SftpDest Adls2Dest].include?(es.dest_type)
      end

      preloader.preload(
        data_source_dests,
        [
          :data_source,
        ],
      )

      list.to_a
    end

    sig do
      params(fetched_items: T::Array[ApplicationRecord]).returns(ActiveRecord::Services::RelationPaginator::Cursors)
    end
    def build_cursors(fetched_items)
      relation = build_relation
      paginator.build_cursors(relation, fetched_items: fetched_items)
    end

    private

    def build_relation
      relation = EmailSchedule
      relation = apply_joins(relation)
      apply_conditions(relation)
    end

    def apply_joins(relation)
      relation =
        relation
        .joins("LEFT JOIN users U ON #{EmailSchedule.table_name}.creator_id = U.id")
        .joins("LEFT JOIN schedules S ON #{EmailSchedule.table_name}.schedule_id = S.id")
        .joins("LEFT JOIN last_runs LR ON #{EmailSchedule.table_name}.tenant_id = LR.tenant_id AND LR.source_id = #{EmailSchedule.table_name}.id AND LR.source_method = 'execute' AND LR.source_type = 'EmailSchedule'")

      if @source_type == 'QueryReport' || @source_type.nil?
        relation = relation.joins("LEFT JOIN query_reports QR ON (#{EmailSchedule.table_name}.source_type = 'QueryReport' AND #{EmailSchedule.table_name}.source_id = QR.id)")
      end

      if @source_type == 'Dashboard' || @source_type.nil?
        relation = relation.joins("LEFT JOIN dashboards D ON (#{EmailSchedule.table_name}.source_type = 'Dashboard' AND #{EmailSchedule.table_name}.source_id = D.id)")
      end

      relation
    end

    def apply_conditions(relation)
      relation =
        relation
        .where("#{EmailSchedule.table_name}.tenant_id = ?", @tenant_id)
        .where("#{EmailSchedule.table_name}.test_only = FALSE")

      relation = relation.where('QR.id = ?', @source_id.to_i) if @source_type == 'QueryReport' && @source_id.present?

      relation = relation.where('D.id = ?', @source_id.to_i) if @source_type == 'Dashboard' && @source_id.present?

      relation = relation.where("#{EmailSchedule.table_name}.source_type = ?", @source_type) if @source_type.present?

      relation = relation.where("#{EmailSchedule.table_name}.dest_type = ?", @dest_type) if @dest_type.present?

      relation
    end

    sig { returns(ActiveRecord::Services::RelationPaginator) }
    def paginator
      @paginator ||= ActiveRecord::Services::RelationPaginator.new
    end

    def select_list(relation)
      relation =
        relation
        .select("#{EmailSchedule.table_name}.id, #{EmailSchedule.table_name}.source_id, #{EmailSchedule.table_name}.source_type")
        .select("#{EmailSchedule.table_name}.schedule_id, #{EmailSchedule.table_name}.creator_id, #{EmailSchedule.table_name}.dest_id")
        .select("#{EmailSchedule.table_name}.dest_type, #{EmailSchedule.table_name}.tenant_id")
        .select("#{EmailSchedule.table_name}.title")
        .select('U.name AS creator_name, S.repeat AS schedule_repeat, LR.job_id AS last_run_job_id, LR.job_status AS last_run_job_status, LR.start_time AS last_run_job_start_time')

      relation =
        if @source_type == 'QueryReport'
          relation.select('QR.title as source_title')
        elsif @source_type == 'Dashboard'
          relation.select('D.title as source_title')
        else
          relation.select('coalesce(QR.title, D.title) as source_title')
        end

      if @source_type == 'Dashboard' || @source_type.nil?
        relation.select('COALESCE(D.version, 1) = 3 as is_v3')
      else
        relation.select('false as is_v3')
      end
    end
  end
end
