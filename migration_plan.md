# Data Schedules Migration Plan

## Current Implementation Analysis

### Frontend (DataSchedules.vue)
- **Current State**: Uses cursor-based pagination with `usePagination` composable from `@holistics/ds`
- **Pagination**: Implemented with `HPaginationCursor` component
- **Search**: No search functionality currently implemented
- **Sort**: Basic sorting through `HTable` component with `listingOptions.sortable`
- **Data Management**: Local state management with reactive updates

### Backend (EmailSchedules::IndexQuery)
- **Current State**: Uses `ActiveRecord::Services::RelationPaginator` for cursor-based pagination
- **Pagination**: Supports `before`, `after`, and `limit` parameters
- **Search**: No search functionality implemented
- **Sort**: Uses natural sorting by ID only
- **Performance**: Includes comprehensive preloading and joins

## Migration Plan

### Phase 1: Backend Search Implementation

#### 1.1 Update EmailSchedules::IndexQuery
**File**: `app/queries/email_schedules/index_query.rb`

**Changes**:
- Add `search_term` parameter to constructor
- Implement search functionality in `apply_conditions` method
- Add search on `title` field using ILIKE for case-insensitive search
- Follow the same pattern as DataAlerts::IndexQuery

**Implementation**:
```ruby
# Add to constructor
const :search_term, T.nilable(String), default: nil

# Update apply_conditions method
def apply_conditions(relation)
  # ... existing conditions ...
  relation = relation.where('title ilike ?', "%#{search_term}%") if search_term.present?
  relation
end
```

#### 1.2 Update DataSchedulesController
**File**: `app/controllers/api/v2/data_schedules_controller.rb`

**Changes**:
- Include `Concerns::ListingParams` module for search functionality
- Update `index` action to accept `search_term` parameter
- Pass `search_term` to `EmailSchedules::IndexQuery`

**Implementation**:
```ruby
include Concerns::ListingParams

def index
  # ... existing authorization ...

  ds_index_query = EmailSchedules::IndexQuery.new(
    current_tenant.id,
    dest_type: params[:dest_type],
    source_type: params[:source_type],
    source_id: params[:source_id],
    search_term: search_term,
    before: pagination_params[:before],
    after: pagination_params[:after],
    limit: pagination_params[:limit],
  )

  # ... rest of implementation
end
```

#### 1.3 Update OpenAPI Documentation
**File**: `openapi/lib/operations/data_schedules/List.yml`

**Changes**:
- Add search_term parameter reference following Data Alerts pattern
- Include proper parameter documentation

**Implementation**:
```yaml
parameters:
  - $ref: ../../components/parameters/pagination/Before.yml
  - $ref: ../../components/parameters/pagination/After.yml
  - $ref: ../../components/parameters/pagination/Limit.yml
  - $ref: ../../components/parameters/SearchTerm.yml  # Add this line
  - name: dest_type
    # ... existing parameters
```

**Note**: The existing `SearchTerm.yml` component already provides:
- Parameter name: `search_term`
- Type: `string`
- Description: "Search records by its name, title..."
- Follows OpenAPI 3.0 specification standards

### Phase 2: Enhanced Sorting Implementation

#### 2.1 Update RelationPaginator Mapping
**File**: `app/queries/email_schedules/index_query.rb`

**Changes**:
- Enhance paginator mapping to support multiple sort options
- Add sorting by title, destination, schedule, and last run
- Follow the pattern from DataAlerts::IndexQuery

**Implementation**:
```ruby
def paginator
  @paginator ||= ActiveRecord::Services::RelationPaginator.new(
    mapping: {
      natural: [[:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      id_asc: [[:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      id_desc: [[:id, ActiveRecord::Services::RelationPaginator::Direction::Desc]],
      title_asc: [[:title, ActiveRecord::Services::RelationPaginator::Direction::Asc], [:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      title_desc: [[:title, ActiveRecord::Services::RelationPaginator::Direction::Desc], [:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      # Add more sorting options as needed
    }
  )
end
```

#### 2.2 Update Controller for Sort Parameters
**File**: `app/controllers/api/v2/data_schedules_controller.rb`

**Changes**:
- Accept `sort` parameter in index action
- Pass sort parameter to IndexQuery
- Validate sort parameter values

### Phase 3: Frontend Search Implementation

#### 3.1 Add Search Component
**File**: `app/javascript/modules/DashboardAsCode/components/dashboard/preferences/DataSchedules.vue`

**Changes**:
- Import `SearchBox` component
- Add search state management
- Implement search functionality with debouncing
- Follow the pattern from DataAlerts.vue

**Implementation**:
```vue
<template>
  <!-- Add search box before the table -->
  <div class="mb-2 flex items-center">
    <span class="mr-2">Filter: </span>
    <search-box
      v-model="searchTerm"
      placeholder="Search schedules"
      clearable
    />
  </div>
  <!-- ... existing table implementation -->
</template>

<script setup lang="ts">
// Add search state
const searchTerm = ref('');

// Watch for search changes
watch(searchTerm, () => {
  const succeeded = pagination.toFirstPage();
  if (!succeeded) {
    pagination.fetchData();
  }
});

// Update fetchAllDataSchedules to include search
async function fetchAllDataSchedules(paginationParams: PageState) {
  // ... existing implementation ...
  const response = await fetchDataSchedules({
    sourceId: Number(props.source.data.id),
    sourceType: props.source.sourceType as 'Dashboard',
    search_term: searchTerm.value,
    ...paginationParams,
  });
  // ... rest of implementation
}
</script>
```

### Phase 4: Enhanced Table Functionality

#### 4.1 Update Table Configuration
**File**: `app/javascript/modules/DashboardAsCode/components/dashboard/preferences/DataSchedules.vue`

**Changes**:
- Update `listingOptions` to support more sorting options
- Enhance table responsiveness
- Improve column configurations

#### 4.2 Add Advanced Filtering (Optional)
- Consider adding filters by destination type
- Add filters by schedule status (active/paused)
- Add date range filters for last run

### Phase 5: Performance Optimizations

#### 5.1 Database Optimizations
**Considerations**:
- Add database indexes for search performance
- Optimize joins in IndexQuery
- Consider adding composite indexes for common sort/filter combinations

#### 5.2 Frontend Optimizations
**Considerations**:
- Implement search debouncing
- Add loading states for better UX
- Consider virtual scrolling for large datasets

## Implementation Sequence

### Step 1: Backend Search (Priority: High)
1. Update `EmailSchedules::IndexQuery` to support search
2. Update `DataSchedulesController` to accept search parameters
3. **Update OpenAPI documentation** in `openapi/lib/operations/data_schedules/List.yml`
4. Test search functionality following established patterns

### Step 2: Frontend Search Integration (Priority: High)
1. Add search component to DataSchedules.vue
2. Integrate search with pagination
3. Test search with pagination using reference test patterns
4. Implement E2E tests following `spec/integration/dashboards_v4/data_schedules_spec.rb`

### Step 3: Enhanced Sorting (Priority: Medium)
1. Implement backend sorting options
2. Update frontend to support multiple sort options
3. Update OpenAPI documentation for new sort parameters
4. Test sorting functionality

### Step 4: Performance and Polish (Priority: Low)
1. Add database indexes for search performance
2. Implement advanced filtering options
3. Performance testing and optimization
4. Final documentation updates

## Testing Strategy

### Backend Tests
**Reference Pattern**: Follow `spec/integration/dashboards_v4/data_schedules_spec.rb`

**Unit Tests for EmailSchedules::IndexQuery**:
- Test search functionality with various search terms
- Test search with empty/nil search terms
- Test search combined with pagination parameters
- Test search with different source types and filters

**Controller Tests**:
- Test `search_term` parameter acceptance and validation
- Test search parameter integration with existing filters
- Test API response format with search results
- Follow existing controller test patterns for parameter handling

**Integration Tests**:
- Test search with pagination combinations
- Test search performance with large datasets
- Test search with various user permissions
- Use established data setup and teardown procedures from reference file

### Frontend Tests
**Reference Pattern**: Follow established testing patterns from `spec/integration/dashboards_v4/data_schedules_spec.rb`

**Component Tests**:
- Test SearchBox component integration
- Test search term reactive updates
- Test pagination reset on search changes
- Test search with empty results handling

**Integration Tests**:
- Test complete search workflow with pagination
- Test search functionality across different user roles
- Test search with various dashboard configurations
- Use `safe_click`, `wait_for_element_load`, and other established helpers

**E2E Tests**:
- Test search functionality in browser environment
- Test search with real API responses
- Test search performance and user experience
- Follow existing test structure and organization patterns

### Test Implementation Guidelines

**Data Setup**:
- Use FactoryBot patterns from reference file
- Create test schedules with various titles for search testing
- Set up proper user permissions and tenant configurations
- Follow established context and shared example patterns

**Assertion Patterns**:
- Use `expect(page.find('[data-ci="..."]').text)` patterns
- Follow `wait_expect` patterns for async operations
- Use established CSS selectors and data-ci attributes
- Test both positive and negative search scenarios

**Test Organization**:
- Group search tests in logical contexts
- Use shared examples for common search scenarios
- Follow established naming conventions
- Include both unit and integration test coverage

## Backward Compatibility

- All changes maintain backward compatibility
- Existing API endpoints continue to work without search parameters
- Frontend gracefully handles missing search functionality

## Risk Assessment

### Low Risk
- Search implementation follows established patterns
- Pagination system is already robust
- Changes are additive, not destructive

### Mitigation Strategies
- Implement feature flags for gradual rollout
- Comprehensive testing before deployment
- Monitor performance impact after deployment

## Success Criteria

1. **Search Functionality**: Users can search schedules by title
2. **Performance**: Search results return within 500ms for typical datasets
3. **User Experience**: Search integrates seamlessly with existing pagination
4. **Backward Compatibility**: Existing functionality remains unchanged
5. **Code Quality**: Implementation follows established patterns and conventions

## Key Implementation Details

### Current Pagination System
The current implementation uses:
- **Backend**: `ActiveRecord::Services::RelationPaginator` with cursor-based pagination
- **Frontend**: `usePagination` composable from `@holistics/ds` with `HPaginationCursor`
- **Cursor Format**: String-based cursors (e.g., "123" for ID-based pagination)
- **API Response**: `{ data_schedules: [...], cursors: { next: "...", previous: "..." } }`

### Search Integration Points
1. **Backend Query**: Add ILIKE search on `email_schedules.title` field
2. **Controller**: Accept `search_term` parameter via `Concerns::ListingParams`
3. **Frontend**: Add `SearchBox` component with reactive search term
4. **Pagination Reset**: Search changes trigger `pagination.toFirstPage()`

### Data Flow
1. User types in search box → `searchTerm` reactive variable updates
2. Watch handler triggers → `pagination.toFirstPage()` or `pagination.fetchData()`
3. Frontend calls `fetchDataSchedules()` with search term
4. Backend `EmailSchedules::IndexQuery` applies search filter
5. Results returned with updated cursors for pagination

This plan ensures a smooth migration following proven patterns from the Data Alerts implementation while maintaining the robust cursor-based pagination system already in place.

## Summary of Key Additions

### OpenAPI Documentation Requirements
- **File**: `openapi/lib/operations/data_schedules/List.yml`
- **Change**: Add `$ref: ../../components/parameters/SearchTerm.yml` to parameters list
- **Benefit**: Leverages existing SearchTerm component with proper OpenAPI 3.0 specification
- **Pattern**: Follows Data Alerts API documentation structure exactly

### Enhanced Testing Strategy
- **Reference File**: `spec/integration/dashboards_v4/data_schedules_spec.rb`
- **Key Patterns**: Use established FactoryBot patterns, data-ci selectors, and helper methods
- **Test Structure**: Follow existing context organization and shared example patterns
- **Coverage**: Include both positive/negative search scenarios with pagination combinations

### Implementation Priorities
1. **Phase 1**: Backend search + OpenAPI documentation updates
2. **Phase 2**: Frontend integration with comprehensive testing
3. **Phase 3**: Enhanced sorting with documentation
4. **Phase 4**: Performance optimization and final polish

The migration maintains backward compatibility while adding robust search functionality that integrates seamlessly with the existing cursor-based pagination system.
