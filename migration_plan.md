# Data Schedules Migration Plan

## Current Implementation Analysis

### Frontend (DataSchedules.vue)
- **Current State**: Uses cursor-based pagination with `usePagination` composable from `@holistics/ds`
- **Pagination**: Implemented with `HPaginationCursor` component
- **Search**: No search functionality currently implemented
- **Sort**: Basic sorting through `HTable` component with `listingOptions.sortable`
- **Data Management**: Local state management with reactive updates

### Backend (EmailSchedules::IndexQuery)
- **Current State**: Uses `ActiveRecord::Services::RelationPaginator` for cursor-based pagination
- **Pagination**: Supports `before`, `after`, and `limit` parameters
- **Search**: No search functionality implemented
- **Sort**: Uses natural sorting by ID only
- **Performance**: Includes comprehensive preloading and joins

## Migration Plan

### Phase 1: Backend Search Implementation

#### 1.1 Update EmailSchedules::IndexQuery
**File**: `app/queries/email_schedules/index_query.rb`

**Changes**:
- Add `search_term` parameter to constructor
- Implement search functionality in `apply_conditions` method
- Add search on `title` field using ILIKE for case-insensitive search
- Follow the same pattern as DataAlerts::IndexQuery

**Implementation**:
```ruby
# Add to constructor
const :search_term, T.nilable(String), default: nil

# Update apply_conditions method
def apply_conditions(relation)
  # ... existing conditions ...
  relation = relation.where('title ilike ?', "%#{search_term}%") if search_term.present?
  relation
end
```

#### 1.2 Update DataSchedulesController
**File**: `app/controllers/api/v2/data_schedules_controller.rb`

**Changes**:
- Include `Concerns::ListingParams` module for search functionality
- Update `index` action to accept `search_term` parameter
- Pass `search_term` to `EmailSchedules::IndexQuery`

**Implementation**:
```ruby
include Concerns::ListingParams

def index
  # ... existing authorization ...

  ds_index_query = EmailSchedules::IndexQuery.new(
    current_tenant.id,
    dest_type: params[:dest_type],
    source_type: params[:source_type],
    source_id: params[:source_id],
    search_term: search_term,
    before: pagination_params[:before],
    after: pagination_params[:after],
    limit: pagination_params[:limit],
  )

  # ... rest of implementation
end
```

#### 1.3 Update OpenAPI Documentation
**File**: `openapi/lib/operations/data_schedules/List.yml`

**Changes**:
- Add search_term parameter reference following Data Alerts pattern
- Include proper parameter documentation

**Implementation**:
```yaml
parameters:
  - $ref: ../../components/parameters/pagination/Before.yml
  - $ref: ../../components/parameters/pagination/After.yml
  - $ref: ../../components/parameters/pagination/Limit.yml
  - $ref: ../../components/parameters/SearchTerm.yml  # Add this line
  - name: dest_type
    # ... existing parameters
```

**Note**: The existing `SearchTerm.yml` component already provides:
- Parameter name: `search_term`
- Type: `string`
- Description: "Search records by its name, title..."
- Follows OpenAPI 3.0 specification standards

### Phase 2: Enhanced Sorting Implementation

#### 2.1 Update RelationPaginator Mapping
**File**: `app/queries/email_schedules/index_query.rb`

**Changes**:
- Enhance paginator mapping to support multiple sort options
- Add sorting by title, destination, schedule, and last run
- Follow the pattern from DataAlerts::IndexQuery

**Implementation**:
```ruby
def paginator
  @paginator ||= ActiveRecord::Services::RelationPaginator.new(
    mapping: {
      natural: [[:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      id_asc: [[:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      id_desc: [[:id, ActiveRecord::Services::RelationPaginator::Direction::Desc]],
      title_asc: [[:title, ActiveRecord::Services::RelationPaginator::Direction::Asc], [:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      title_desc: [[:title, ActiveRecord::Services::RelationPaginator::Direction::Desc], [:id, ActiveRecord::Services::RelationPaginator::Direction::Asc]],
      # Add more sorting options as needed
    }
  )
end
```

#### 2.2 Update Controller for Sort Parameters
**File**: `app/controllers/api/v2/data_schedules_controller.rb`

**Changes**:
- Accept `sort` parameter in index action
- Pass sort parameter to IndexQuery
- Validate sort parameter values

### Phase 3: Frontend Search Implementation

#### 3.1 Add Search Component
**File**: `app/javascript/modules/DashboardAsCode/components/dashboard/preferences/DataSchedules.vue`

**Changes**:
- Import `SearchBox` component
- Add search state management
- Implement search functionality with debouncing
- Follow the pattern from DataAlerts.vue

**Implementation**:
```vue
<template>
  <!-- Add search box before the table -->
  <div class="mb-2 flex items-center">
    <span class="mr-2">Filter: </span>
    <search-box
      v-model="searchTerm"
      placeholder="Search schedules"
      clearable
    />
  </div>
  <!-- ... existing table implementation -->
</template>

<script setup lang="ts">
// Add search state
const searchTerm = ref('');

// Watch for search changes
watch(searchTerm, () => {
  const succeeded = pagination.toFirstPage();
  if (!succeeded) {
    pagination.fetchData();
  }
});

// Update fetchAllDataSchedules to include search
async function fetchAllDataSchedules(paginationParams: PageState) {
  // ... existing implementation ...
  const response = await fetchDataSchedules({
    sourceId: Number(props.source.data.id),
    sourceType: props.source.sourceType as 'Dashboard',
    search_term: searchTerm.value,
    ...paginationParams,
  });
  // ... rest of implementation
}
</script>
```

### Phase 4: Enhanced Table Functionality

#### 4.1 Update Table Configuration
**File**: `app/javascript/modules/DashboardAsCode/components/dashboard/preferences/DataSchedules.vue`

**Changes**:
- Update `listingOptions` to support more sorting options
- Enhance table responsiveness
- Improve column configurations

#### 4.2 Add Advanced Filtering (Optional)
- Consider adding filters by destination type
- Add filters by schedule status (active/paused)
- Add date range filters for last run

### Phase 5: Performance Optimizations

#### 5.1 Database Optimizations
**Considerations**:
- Add database indexes for search performance
- Optimize joins in IndexQuery
- Consider adding composite indexes for common sort/filter combinations

#### 5.2 Frontend Optimizations
**Considerations**:
- Implement search debouncing
- Add loading states for better UX
- Consider virtual scrolling for large datasets

## Implementation Sequence

### Step 1: Backend Search (Priority: High)
1. Update `EmailSchedules::IndexQuery` to support search
2. Update `DataSchedulesController` to accept search parameters
3. **Update OpenAPI documentation** in `openapi/lib/operations/data_schedules/List.yml`
4. Test search functionality following established patterns

### Step 2: Frontend Search Integration (Priority: High)
1. Add search component to DataSchedules.vue
2. Integrate search with pagination
3. Test search with pagination using reference test patterns
4. Implement E2E tests following `spec/integration/dashboards_v4/data_schedules_spec.rb`

### Step 3: Enhanced Sorting (Priority: Medium)
1. Implement backend sorting options
2. Update frontend to support multiple sort options
3. Update OpenAPI documentation for new sort parameters
4. Test sorting functionality

### Step 4: Performance and Polish (Priority: Low)
1. Add database indexes for search performance
2. Implement advanced filtering options
3. Performance testing and optimization
4. Final documentation updates

## Testing Strategy

### Integration Tests
**Reference Pattern**: Follow `spec/integration/dashboards_v4/data_schedules_spec.rb`

All testing will be conducted through integration tests that provide comprehensive coverage of the search functionality implementation. This approach follows the established pattern in the reference file and ensures consistency with current data schedules testing methodology.

### Controller Tests
**Reference Pattern**: Follow `spec/controllers/email_schedules_controller_spec.rb`

Update the existing controller test file to include comprehensive tests for the new search functionality added to the controller.

#### Controller Test Implementation
**File**: `spec/controllers/email_schedules_controller_spec.rb`

Add the following test cases to the existing `.index` describe block:

**Search Parameter Acceptance Tests**:
```ruby
context 'with search functionality' do
  let!(:schedule_with_title) do
    FactoryBot.create :email_schedule,
                      tenant_id: get_test_tenant.id,
                      creator_id: admin.id,
                      dest_type: 'EmailDest',
                      dest_id: email_dest.id,
                      title: 'Important Weekly Report'
  end

  let!(:schedule_without_title) do
    FactoryBot.create :email_schedule,
                      tenant_id: get_test_tenant.id,
                      creator_id: admin.id,
                      dest_type: 'EmailDest',
                      dest_id: email_dest.id,
                      title: 'Daily Dashboard Export'
  end

  it 'should accept search_term parameter and filter results' do
    get :index, params: { search_term: 'Weekly' }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result.length).to eq 1
    expect(result.first[:title]).to eq 'Important Weekly Report'
  end

  it 'should return all schedules when search_term is empty' do
    get :index, params: { search_term: '' }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result.length).to be >= 2
  end

  it 'should return empty results for non-matching search' do
    get :index, params: { search_term: 'NonExistentTerm' }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result).to be_empty
  end

  it 'should perform case-insensitive search' do
    get :index, params: { search_term: 'weekly' }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result.length).to eq 1
    expect(result.first[:title]).to eq 'Important Weekly Report'
  end

  it 'should handle search with special characters safely' do
    get :index, params: { search_term: "'; DROP TABLE email_schedules; --" }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result).to be_empty
  end

  it 'should combine search with existing filters' do
    get :index, params: {
      search_term: 'Weekly',
      dest_type: 'EmailDest'
    }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result.length).to eq 1
    expect(result.first[:title]).to eq 'Important Weekly Report'
    expect(result.first[:dest_type]).to eq 'EmailDest'
  end

  it 'should work with pagination parameters' do
    get :index, params: {
      search_term: 'Report',
      limit: 1
    }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result.length).to eq 1

    # Should include pagination cursors
    cursors = JSON.parse(response.body)['cursors']
    expect(cursors).to be_present
  end
end
```

**Search Edge Cases Tests**:
```ruby
context 'search edge cases' do
  it 'should handle nil search_term gracefully' do
    get :index, params: { search_term: nil }, format: :json
    expect(response).to be_successful
  end

  it 'should handle very long search terms' do
    long_search = 'a' * 1000
    get :index, params: { search_term: long_search }, format: :json
    expect(response).to be_successful

    result = JSON.parse(response.body).rsk
    expect(result).to be_empty
  end

  it 'should handle search with only whitespace' do
    get :index, params: { search_term: '   ' }, format: :json
    expect(response).to be_successful

    # Should return all schedules (whitespace treated as empty)
    result = JSON.parse(response.body).rsk
    expect(result.length).to be >= 1
  end
end
```

#### 1. Backend Search Functionality Testing
**API Endpoint Testing**:
- Test search functionality through data schedules API endpoints
- Verify `search_term` parameter acceptance and processing
- Test search with various search terms and edge cases
- Validate search parameter integration with existing filters (dest_type, source_type, source_id)
- Test search combined with pagination parameters (before, after, limit)
- Verify API response format includes correct search results and cursor pagination

#### 2. Frontend Search Component Integration
**Search Component Integration with Pagination**:
- Test SearchBox component integration within DataSchedules.vue
- Verify search term reactive updates trigger pagination reset
- Test search functionality with existing pagination controls
- Validate search results display correctly in the data schedules table
- Test search with empty results and proper fallback handling
- Verify search state persistence during pagination navigation

#### 3. End-to-End Search Workflows
**Browser Environment Testing**:
- Test complete search workflow from user input to results display
- Verify search functionality across different dashboard configurations
- Test search performance and user experience in real browser environment
- Validate search with real API responses and data
- Test search functionality with various schedule titles and content
- Verify search integration with existing export and management workflows

#### 4. Search Parameter Validation and Error Handling
**Parameter Validation**:
- Test search with invalid or malformed search terms
- Verify proper error handling for search parameter edge cases
- Test search with special characters and SQL injection attempts
- Validate search parameter length limits and constraints
- Test search with empty, null, and undefined search terms
- Verify graceful degradation when search functionality is unavailable

#### 5. Search with User Permissions and Dashboard Configurations
**Permission-Based Testing**:
- Test search functionality across different user roles (admin, analyst, viewer)
- Verify search respects existing data schedule permissions
- Test search with various dashboard configurations and source types
- Validate search results filtering based on user access rights
- Test search functionality with different tenant configurations
- Verify search works correctly with canvas and non-canvas dashboards

### Test Implementation Guidelines

**Data Setup Patterns**:
- Use FactoryBot patterns from reference file for creating test data schedules
- Create schedules with various titles for comprehensive search testing
- Set up proper user permissions and tenant configurations following existing patterns
- Use established context blocks and shared example structures
- Follow existing data teardown procedures with `Job.destroy_all` and similar cleanup

**Assertion Patterns**:
- Use `expect(page.find('[data-ci="..."]').text)` patterns for UI validation
- Follow `wait_expect` patterns for async operations and AJAX requests
- Use established CSS selectors and data-ci attributes from reference file
- Implement `safe_click`, `wait_for_element_load`, and other established helpers
- Test both positive and negative search scenarios with proper assertions

**Test Organization**:
- Group search tests in logical context blocks following reference file structure
- Use shared examples for common search scenarios where appropriate
- Follow established naming conventions and test descriptions
- Organize tests by functionality (API, UI, permissions) within integration test structure
- Include comprehensive coverage through integration tests only, eliminating redundant unit/component tests

## Backward Compatibility

- All changes maintain backward compatibility
- Existing API endpoints continue to work without search parameters
- Frontend gracefully handles missing search functionality

## Risk Assessment

### Low Risk
- Search implementation follows established patterns
- Pagination system is already robust
- Changes are additive, not destructive

### Mitigation Strategies
- Implement feature flags for gradual rollout
- Comprehensive testing before deployment
- Monitor performance impact after deployment

## Success Criteria

1. **Search Functionality**: Users can search schedules by title
2. **Performance**: Search results return within 500ms for typical datasets
3. **User Experience**: Search integrates seamlessly with existing pagination
4. **Backward Compatibility**: Existing functionality remains unchanged
5. **Code Quality**: Implementation follows established patterns and conventions

## Key Implementation Details

### Current Pagination System
The current implementation uses:
- **Backend**: `ActiveRecord::Services::RelationPaginator` with cursor-based pagination
- **Frontend**: `usePagination` composable from `@holistics/ds` with `HPaginationCursor`
- **Cursor Format**: String-based cursors (e.g., "123" for ID-based pagination)
- **API Response**: `{ data_schedules: [...], cursors: { next: "...", previous: "..." } }`

### Search Integration Points
1. **Backend Query**: Add ILIKE search on `email_schedules.title` field
2. **Controller**: Accept `search_term` parameter via `Concerns::ListingParams`
3. **Frontend**: Add `SearchBox` component with reactive search term
4. **Pagination Reset**: Search changes trigger `pagination.toFirstPage()`

### Data Flow
1. User types in search box → `searchTerm` reactive variable updates
2. Watch handler triggers → `pagination.toFirstPage()` or `pagination.fetchData()`
3. Frontend calls `fetchDataSchedules()` with search term
4. Backend `EmailSchedules::IndexQuery` applies search filter
5. Results returned with updated cursors for pagination

This plan ensures a smooth migration following proven patterns from the Data Alerts implementation while maintaining the robust cursor-based pagination system already in place.

## Summary of Key Additions

### OpenAPI Documentation Requirements
- **File**: `openapi/lib/operations/data_schedules/List.yml`
- **Change**: Add `$ref: ../../components/parameters/SearchTerm.yml` to parameters list
- **Benefit**: Leverages existing SearchTerm component with proper OpenAPI 3.0 specification
- **Pattern**: Follows Data Alerts API documentation structure exactly

### Enhanced Testing Strategy
- **Reference Files**:
  - `spec/integration/dashboards_v4/data_schedules_spec.rb` for integration tests
  - `spec/controllers/email_schedules_controller_spec.rb` for controller tests
- **Testing Approach**: Integration tests + Controller tests for comprehensive coverage
- **Key Areas**: Backend API testing, controller parameter handling, frontend search integration, user permissions, parameter validation
- **Test Patterns**: Use established FactoryBot patterns, data-ci selectors, and helper methods
- **Test Structure**: Follow existing context organization and shared example patterns
- **Coverage**: Controller search functionality, backend search functionality, frontend integration, E2E workflows, permissions, and error handling

### Implementation Priorities
1. **Phase 1**: Backend search + OpenAPI documentation updates
2. **Phase 2**: Frontend integration with comprehensive testing
3. **Phase 3**: Enhanced sorting with documentation
4. **Phase 4**: Performance optimization and final polish

The migration maintains backward compatibility while adding robust search functionality that integrates seamlessly with the existing cursor-based pagination system.
