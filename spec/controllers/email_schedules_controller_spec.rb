# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe EmailSchedulesController do
  let(:admin) { get_test_admin }
  before do
    sign_in admin
    FeatureToggle.toggle_global('integrations:slack', true)
    ActionMailer::Base.deliveries.clear
  end
  let!(:email_dest) { FactoryBot.create :email_dest }
  let!(:es) do
    FactoryBot.create :email_schedule, tenant_id: get_test_tenant.id, creator_id: admin.id,
                                        dest_type: 'EmailDest', dest_id: email_dest.id
  end

  let!(:es_ms) do
    FactoryBot.create :email_schedule_ms, creator_id: admin.id,
                                          dest_type: 'EmailDest', dest_id: email_dest.id
  end

  let!(:dashboard) { FactoryBot.create :dashboard }
  let!(:query_report) { FactoryBot.create :query_report }
  let!(:dashboard_widget) do
    FactoryBot.create(
      :dashboard_widget,
      dashboard: dashboard,
      tenant: dashboard.tenant,
      source: query_report,
      title: 'Dashboard Widget',
    )
  end
  let!(:error_report) { FactoryBot.create :query_report, query: 'failed query' }
  let!(:schedules) do
    FactoryBot.create :schedule,
                       start_date: Time.new,
                       repeat: '* * * * *'
  end
  let(:tenant) { get_test_tenant }
  let(:slack_team_id) { 'ST315' }
  let(:es_params) do
    {
      email_schedule: {
        source_id: dashboard.id,
        source_type: 'Dashboard',
        filter_values: [],
        schedule: schedules.slice(:start_date, :repeat),
        dest_type: 'EmailDest',
        dest: {
          title: '123',
          recipients: ['<EMAIL>'],
        },
      },
    }
  end
  let(:gsheet_params) do
    {
      email_schedule: {
        source_id: query_report.id,
        source_type: 'QueryReport',
        filter_values: [],
        schedule: schedules.slice(:start_date, :repeat),
        dest_type: 'GsheetDest',
        dest: {
          sheet_url: 'https://docs.google.com/spreadsheets/d/1fQuKbYBo7PYVkXCJL4CpE_luCOsWGN03ObTq0Gh8NW4',
          mapping: [{"widget_id"=> dashboard_widget, "worksheet_id"=> 1, "worksheet_title"=>"Sheet"}],
        },
      },
    }
  end

  def add_slack_team_to_tenant
    tenant.settings[:slack_team_id] = slack_team_id
    tenant.settings[:slack_enabled] = true
    tenant.save!
  end

  describe '.index' do
    it 'should raise error when input parameters are invalid' do
      get :index, params: {}, format: :json
      expect(response).to be_successful

      result = JSON.parse(response.body).rsk
      expect(result.first).to be_truthy
      expect(result.first[:dest]).to be_truthy
      expect(result.first[:dest][:options]).to be_truthy

      get :index, params: { source_type: 'FakeSource', source_id: 1 }, format: :json
      expect(response).to have_http_status(422)
      get :index, params: { source_type: 'QueryReport', source_id: 'sql injection' }, format: :json
      expect(response).to have_http_status(422)
    end

    it 'should filter dest type' do
      get :index, params: { dest_type: 'EmailDest' }, format: :json
      expect(response).to be_successful
      expect(JSON.parse(response.body).rsk.first[:dest_type]).to eql('EmailDest')

      get :index, params: { dest_type: 'SlackDest' }, format: :json
      expect(JSON.parse(response.body)).to be_empty

      get :index, params: { dest_type: 'GsheetDest' }, format: :json
      expect(JSON.parse(response.body)).to be_empty
    end

    context 'with sftp schedules' do
      let(:sftp_data_source) { FactoryBot.create :test_ds_sftp }
      let!(:sftp_dest) { FactoryBot.create :sftp_dest, data_source_id: sftp_data_source.id, tenant_id: sftp_data_source.tenant_id }
      let!(:sftp_ds) do
        FactoryBot.create :email_schedule, tenant_id: get_test_tenant.id, creator_id: admin.id,
                                            dest_type: 'SftpDest', dest_id: sftp_dest.id
      end
      before do
        FeatureToggle.toggle_global(DataSource::SFTP_FEATURE_TOGGLE, true)
      end
      it 'can list sftp schedules' do
        get :index, params: { dest_type: 'SftpDest' }, format: :json
        ds = JSON.parse(response.body).rsk.last
        expect(ds[:dest_type]).to eql('SftpDest')
        expect(ds[:dest][:data_source_id]).to eql(sftp_data_source.id)
        expect(ds[:dest][:connection_name]).to eql(sftp_data_source.name)
        expect(ds[:dest][:full_path]).to eql('/home/<USER>/test/report.csv')
      end
    end

    context 'with adls2 schedules' do
      let(:adls2_ds) do
        create :test_ds_adls2
      end
      let(:adls2_dest) do
        create :adls2_dest,
               data_source: adls2_ds,
               parameterize_csv_headers: true,
               include_header: true,
               separator: "\t"
      end
      let!(:adls2_schedule) do
        create :email_schedule,
               tenant: admin.tenant,
               creator: admin,
               dest: adls2_dest
      end

      it 'can list adls2 schedule' do
        get :index, params: { dest_type: 'Adls2Dest' }, format: :json
        ds = JSON.parse(response.body).rsk.last
        expect(ds[:dest_type]).to eql('Adls2Dest')
        expect(ds[:dest][:data_source_id]).to eql(adls2_ds.id)
        expect(ds[:dest][:storage_name]).to eql(adls2_ds.dbconfig[:storage_name])
        expect(ds[:dest][:container]).to eql(adls2_ds.dbconfig[:container])
        expect(ds[:dest][:file_path]).to eql('test/report')
      end
    end

    context 'with gsheet schedules' do
      let(:gsheet_dest) do
        create :gsheet_dest
      end
      let!(:gsheet_schedule) do
        create :email_schedule,
               tenant: admin.tenant,
               creator: admin,
               dest: gsheet_dest
      end

      it 'can list gsheet schedule' do
        get :index, params: { dest_type: 'GsheetDest' }, format: :json
        ds = JSON.parse(response.body).rsk.last
        expect(ds[:dest_type]).to eql('GsheetDest')
        expect(ds[:dest][:sheet_url]).to eql(gsheet_dest.sheet_url)
        expect(ds[:dest][:sheet_title]).to eql(gsheet_dest.sheet_title)
      end
    end

    describe 'is able to filter slack channels' do
      before do
        dest = FactoryBot.create :slack_dest
        dest.slack_channels << { id: 'ahihi', name: 'ahoho', type: 'private' }
        dest.save
        es.dest = dest
        es.save!
      end

      it 'should show all channels for schedule creator' do
        get :index, params: { dest_type: 'SlackDest' }, format: :json
        expect(JSON.parse(response.body)[0]['dest']['slack_channels'].size).to eq 2
      end

      it 'should show all channels for admin' do
        sign_in create(:capt_america)
        get :index, params: { dest_type: 'SlackDest' }, format: :json
        expect(JSON.parse(response.body)[0]['dest']['slack_channels'].size).to eq 2
      end

      it 'should redact non-public channels for non-creator' do
        anal = create(:analyst)
        sign_in anal
        admin.share(anal, :read, get_report('Avengers report'))
        get :index, params: { dest_type: 'SlackDest' }, format: :json
        expect(JSON.parse(response.body)[0]['dest']['slack_channels'].map { |c| c['name'] }).to eq(['Test Channel', '<private_channel>'])
      end
    end

    describe 'is able to return schedule friendly description in user timezone' do
      let(:new_schedule) { FactoryBot.create :schedule, start_date: Time.new, repeat: '0 0 * * *' }
      before do
        es.update!(schedule: new_schedule)
      end

      it 'should list all schedules in Saigon timezone' do
        set_browser_timezone_header
        get :index, format: :json

        expect(response).to be_successful

        result = JSON.parse(response.body)
        schedule_friendly_desc = result.first.dig('schedule').dig('friendly_description')
        expect(schedule_friendly_desc).to eq('Daily at 7:00 (UTC+0700)')
      end
    end

    context 'with dynamic_filter_presets' do
      let(:dynamic_filter) do
        FactoryBot.create :dynamic_filter, dynamic_filter_holdable: es.source, order: 0
      end
      let!(:dynamic_filter_preset) do
        FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: ['ahihi'], modifier: nil }
      end
      let!(:dynamic_filter_preset2) do
        FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: ['hehehe'], modifier: nil }
      end
      it 'includes the dynamic_filter_presets' do
        get :index, format: :json
        expect(response).to be_successful

        result = JSON.parse(response.body)
        dynamic_filter_presets = result.first['dynamic_filter_presets']
        expect(dynamic_filter_presets.size).to eq 2

        expect(dynamic_filter_presets.first['id']).to eq dynamic_filter_preset.id
        expect(dynamic_filter_presets.first['preset_condition']['values']).to eq ['ahihi']
        expect(dynamic_filter_presets.first['permissions']['read']).to eq true
        expect(dynamic_filter_presets.first['permissions']['crud']).to eq true

        expect(dynamic_filter_presets[1]['id']).to eq dynamic_filter_preset2.id
        expect(dynamic_filter_presets[1]['preset_condition']['values']).to eq ['hehehe']
      end
    end

    context 'with title field' do
      it 'includes title in response when title is present' do
        es.update!(title: 'Test Schedule Title')

        get :index, format: :json
        expect(response).to be_successful

        result = JSON.parse(response.body).rsk
        schedule_with_title = result.find { |s| s[:id] == es.id }
        expect(schedule_with_title).to be_present
        expect(schedule_with_title[:title]).to eq 'Test Schedule Title'
      end

      it 'includes null title in response when title is not set' do
        es.update!(title: nil)

        get :index, format: :json
        expect(response).to be_successful

        result = JSON.parse(response.body).rsk
        schedule_without_title = result.find { |s| s[:id] == es.id }
        expect(schedule_without_title).to be_present
        expect(schedule_without_title[:title]).to be_nil
      end

      it 'includes empty title in response when title is empty string' do
        es.update!(title: '')

        get :index, format: :json
        expect(response).to be_successful

        result = JSON.parse(response.body).rsk
        schedule_with_empty_title = result.find { |s| s[:id] == es.id }
        expect(schedule_with_empty_title).to be_present
        expect(schedule_with_empty_title[:title]).to eq ''
      end
    end
  end

  describe '.show' do
    let(:other_user) { create :user, role: 'analyst' }
    before do
      dest = FactoryBot.create :slack_dest
      dest.slack_channels << { id: 'ahihi', name: 'ahoho', type: 'private' }
      dest.save
      es.dest = dest
      es.save!
      admin.share(other_user, :read, es.source)
    end

    it 'should redact non-public channels for non-creator' do
      sign_in other_user
      get :show, params: { id: es.id }, format: :json
      assert_success_response!
      expect(JSON.parse(response.body)['dest']['slack_channels'].map { |c| c['name'] }).to eq(['Test Channel', '<private_channel>'])
    end

    it 'can show metric sheet source type' do
      sign_in other_user
      get :show, params: { id: es_ms.id }, format: :json
      assert_success_response!
      json_body = JSON.parse(response.body)
      expect(json_body['source']['title']).to eq es_ms.source.title
      expect(json_body['source']).to have_key('can_share')
      expect(json_body['source']['can_share']).to be_nil
      expect(json_body['source']).to have_key('can_update')
      expect(json_body['source']['can_update']).to be_nil
    end

    describe 'is able to return schedule friendly description in user timezone' do
      let(:new_schedule) { FactoryBot.create :schedule, start_date: Time.new, repeat: '0 0 * * *' }
      before do
        es.update!(schedule: new_schedule)
      end

      it 'shoule fetch schedule in Saigon timezone' do
        set_browser_timezone_header
        get :show, params: { id: es.id }, format: :json
        expect(response).to be_successful
        result = JSON.parse(response.body)
        friendly_desc = result.dig('schedule').dig('friendly_description')
        expect(friendly_desc).to eq('Daily at 7:00 (UTC+0700)')
      end
    end

    context 'with dynamic_filter_presets' do
      let(:dynamic_filter) do
        FactoryBot.create :dynamic_filter, dynamic_filter_holdable: es.source, order: 0
      end
      let!(:dynamic_filter_preset) do
        FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: ['ahihi'], modifier: nil }
      end
      let!(:dynamic_filter_preset2) do
        FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: ['hehehe'], modifier: nil }
      end
      it 'includes the dynamic_filter_presets' do
        get :show, params: { id: es.id }, format: :json
        expect(response).to be_successful

        result = JSON.parse(response.body)
        dynamic_filter_presets = result['dynamic_filter_presets']
        expect(dynamic_filter_presets.size).to eq 2

        expect(dynamic_filter_presets.first['id']).to eq dynamic_filter_preset.id
        expect(dynamic_filter_presets.first['preset_condition']['values']).to eq ['ahihi']
        expect(dynamic_filter_presets.first['permissions']['read']).to eq true
        expect(dynamic_filter_presets.first['permissions']['crud']).to eq true

        expect(dynamic_filter_presets[1]['id']).to eq dynamic_filter_preset2.id
        expect(dynamic_filter_presets[1]['preset_condition']['values']).to eq ['hehehe']
      end
    end
  end

  describe '.create' do
    context 'when source is of different tenant' do
      it 'gives error' do
        t2 = create :tenant
        dashboard2 = create :dashboard, tenant: t2, owner_id: get_test_user.id
        params2 = es_params.dup.tap { |p| p[:email_schedule][:source_id] = dashboard2.id }
        post :create, params: params2
        expect(response.status).not_to eql 200
      end
    end

    context '.schedule' do
      it 'should update schedule with the current user tenant' do
        post :create, params: es_params

        expect(response).to be_successful
        schedule = Schedule.last
        expect(schedule.tenant_id).to eq tenant.id
      end
    end

    context '.email_dest' do
      context 'with invalid option' do
        it 'should throw error when option include_filters is string' do
          es_params[:email_schedule][:dest][:options] = {
            include_filters: 'invalid',
          }
          post :create, params: es_params, format: :json
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.body).to include 'Option include_filters must be a boolean'
        end
        it 'should throw error when option include_report_link is string' do
          es_params[:email_schedule][:dest][:options] = {
            include_report_link: 'invalid',
          }
          post :create, params: es_params, format: :json
          expect(response).to have_http_status(:unprocessable_entity)
          expect(response.body).to include 'Option include_report_link must be a boolean'
        end
      end

      it 'should create email schedule with email dest' do
        post :create, params: es_params
        expect(response.status).to eql 200
        es = EmailSchedule.last
        expect(es.source.id).to eql dashboard.id
        expect(es.dest.recipients).to eql es_params[:email_schedule][:dest][:recipients]
        expect(es.dest.options[:base_url]).to be_present
        expect(es.schedule.repeat).to eql es_params[:email_schedule][:schedule][:repeat]
      end

      context 'when users want to protect attachment with password' do
        it 'validates presence of password' do
          es_params[:email_schedule][:dest][:options] = { password_enabled: true, format: 'excel' }
          post :create, params: es_params

          expect(response.status).to eq 422
          expect(JSON.parse(response.body)['errors'][0]).to include('must have at least')
        end

        it 'ignores password if no attachment' do
          es_params[:email_schedule][:dest][:options] = { password_enabled: true, format: 'none', password: '1' }
          post :create, params: es_params

          expect(response.status).to eq 200
          expect(EmailSchedule.last.dest.options[:password]).to eq nil
        end
      end

      context 'with dynamic_filter_presets' do
        let!(:dynamic_filter) do
          FactoryBot.create :dynamic_filter, dynamic_filter_holdable: es.source, order: 0
        end
        let(:dynamic_filter_presets_param) do
          [
            { dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [2], modifier: nil } },
          ]
        end
        before do
          es_params[:email_schedule][:dynamic_filter_presets] = dynamic_filter_presets_param
          request.headers['Content-Type'] = 'application/json'
        end

        it 'creates email schedule with dynamic_filter_presets' do
          post :create, params: es_params
          assert_success_response!

          es = EmailSchedule.last
          expect(es.source.id).to eql dashboard.id
          expect(es.dest.recipients).to eql es_params[:email_schedule][:dest][:recipients]
          expect(es.dynamic_filter_presets.size).to eq 1
          expect(es.dynamic_filter_presets.first.preset_condition.values).to eq [2]
        end

        context 'preset_condition has no values, but values are required' do
          let(:dynamic_filter_presets_param) do
            [
              { dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [], modifier: nil } },
            ]
          end
          it 'keeps the preset_condition as-is' do
            post :create, params: es_params
            assert_success_response!

            es = EmailSchedule.last
            expect(es.dynamic_filter_presets.size).to eq 1
            expect(es.dynamic_filter_presets.first.preset_condition.operator).to eq 'is'
            expect(es.dynamic_filter_presets.first.preset_condition.values).to eq []
          end
        end
      end
    end

    context '.slack_dest' do
      let(:dest_options) { nil }
      let(:params) do
        {
          email_schedule: {
            source_id: query_report.id,
            source_type: 'QueryReport',
            filter_values: [],
            schedule: schedules.slice(:start_date, :repeat),
            dest_type: 'SlackDest',
            dest: {
              title: 'party hard',
              message: 'dont buy TRX',
              slack_channels: [{ id: 'C112', name: 'Test Channel', type: 'public' }],
              options: dest_options,
            },
          },
        }
      end
      before do
        request.headers['Content-Type'] = 'application/json'
      end

      it 'should throw error if tenant hasnt integrated with Slack' do
        post :create, params: params
        expect(response.status).to eql 422
        expect(response.body).to include('integrated with Slack')
      end

      it 'should create data schedule with slack des' do
        add_slack_team_to_tenant
        post :create, params: params
        expect(response.status).to eql 200
        es = EmailSchedule.last
        expect(es.dest_type).to eql 'SlackDest'
        expect(es.schedule.repeat).to eql params[:email_schedule][:schedule][:repeat]
        expect(es.dest.slack_channels.sort).to eql params[:email_schedule][:dest][:slack_channels]
        expect(es.dest.slack_team_id).to eql slack_team_id
        expect(es.dest.options['attachment_formats']).to eql ['png']
      end

      context 'empty attachment' do
        let(:dest_options) do
          {
            'attachment_formats' => [],
          }
        end

        it 'should create data schedule with slack des' do
          add_slack_team_to_tenant
          post :create, params: params
          assert_success_response!
          es = EmailSchedule.last
          expect(es.dest_type).to eql 'SlackDest'
          expect(es.schedule.repeat).to eql params[:email_schedule][:schedule][:repeat]
          expect(es.dest.slack_channels.sort).to eql params[:email_schedule][:dest][:slack_channels]
          expect(es.dest.slack_team_id).to eql slack_team_id
          expect(es.dest.options['attachment_formats']).to eql []
        end
      end
    end

    context 'gsheet_dest' do
      it 'should create data schedule with gsheet dest' do
        create :google_authentication_export, owner: admin
        VCR.use_cassette('google/google_sheet_get') do
          post :create, params: gsheet_params
        end
        expect(response.status).to eql 200
        es = EmailSchedule.last
        expect(es.dest_type).to eql 'GsheetDest'
        expect(es.schedule.repeat).to eql gsheet_params[:email_schedule][:schedule][:repeat]
        expect(es.dest.sheet_url).to eql gsheet_params[:email_schedule][:dest][:sheet_url]
      end

      it 'should raise error if creator do not have auth' do
        post :create, params: gsheet_params
        expect(response.status).to eql 422
      end

      it 'should raise error for invalid url' do
        params = gsheet_params
        current_ds_count = EmailSchedule.count
        params[:email_schedule][:dest][:sheet_url] = 'not_valid'
        post :create, params: gsheet_params
        expect(response.status).to eql 422
        # There should not be a new data schedule
        expect(EmailSchedule.count).to eq current_ds_count
      end
    end
  end

  describe '.update' do
    context('.email_dest') do
      let(:params) do
        hash = JSON.parse(es.to_json).rsk
        hash[:dest] = email_dest_hash
        { id: es.id, email_schedule: hash }
      end
      let(:email_dest_hash) do
        {
          title: 'undress me',
          options: {
            format: 'csv',
            base_url: 'should not be changed',
          },
        }
      end

      it 'can update an existing email schedule' do
        post :update, params: params
        es.reload
        expect(es.dest.options[:format]).to eq('csv')
        expect(es.dest.options[:base_url]).to_not eql(email_dest_hash[:options][:base_url])
      end

      context 'password-protected attachment' do
        it 'validates password length' do
          email_dest_hash[:options] = { password_enabled: true, format: 'excel', password: '1' }
          post :update, params: params

          expect(response.status).to eq 422
          expect(JSON.parse(response.body)['errors'][0]).to include('must have at least')
        end

        it 'keeps old password if no new password is submitted' do
          hash = JSON.parse(es.to_json).recursive_symbolize_keys
          hash[:dest] = email_dest_hash

          # Updates successfully
          hash[:dest][:options] = { password_enabled: true, password: '1234', format: 'excel' }
          post :update, params: { id: es.id, email_schedule: hash }
          dest = es.dest
          expect(dest[:options][:password]).to eq '1234'

          # Keeps old password
          hash[:dest][:options] = { password_enabled: false, format: 'excel' }
          post :update, params: { id: es.id, email_schedule: hash }
          expect(response.status).to eq 200
          dest.reload
          expect(dest[:options][:password]).to eq '1234'
        end
      end

      context 'with title field' do
        it 'can update email schedule title' do
          original_title = 'Original Title'
          es.update!(title: original_title)

          hash = JSON.parse(es.to_json).rsk
          hash[:title] = 'Updated Title'
          hash[:dest] = email_dest_hash
          test_params = { id: es.id, email_schedule: hash }

          post :update, params: test_params
          expect(response.status).to eq 200

          es.reload
          expect(es.title).to eq 'Updated Title'
        end

        it 'can update email schedule title to empty string' do
          es.update!(title: 'Some Title')

          hash = JSON.parse(es.to_json).rsk
          hash[:title] = ''
          hash[:dest] = email_dest_hash
          test_params = { id: es.id, email_schedule: hash }

          post :update, params: test_params
          expect(response.status).to eq 200

          es.reload
          expect(es.title).to eq ''
        end

        it 'can update email schedule title to nil' do
          es.update!(title: 'Some Title')

          hash = JSON.parse(es.to_json).rsk
          hash[:title] = nil
          hash[:dest] = email_dest_hash
          test_params = { id: es.id, email_schedule: hash }

          post :update, params: test_params
          expect(response.status).to eq 200

          es.reload
          # Rails converts nil to empty string in strong parameters
          expect(es.title).to eq ''
        end

        it 'preserves existing title when not provided in update params' do
          original_title = 'Preserved Title'
          es.update!(title: original_title)

          # Update without title field using the existing pattern
          test_params = params.dup
          test_params[:email_schedule][:title] = original_title

          post :update, params: test_params
          expect(response.status).to eq 200

          es.reload
          expect(es.title).to eq original_title
        end

        it 'can update email schedule with long title' do
          long_title = 'B' * 256

          hash = JSON.parse(es.to_json).rsk
          hash[:title] = long_title
          hash[:dest] = email_dest_hash
          test_params = { id: es.id, email_schedule: hash }

          post :update, params: test_params
          expect(response.status).to eq 200

          es.reload
          expect(es.title).to eq long_title
        end
      end

      context 'with dynamic_filter_presets' do
        let!(:dynamic_filter) do
          FactoryBot.create :dynamic_filter, dynamic_filter_holdable: es.source, order: 0
        end
        let!(:dynamic_filter_preset) do
          FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: [1], modifier: nil }
        end
        let(:dynamic_filter_presets_param) do
          []
        end
        before do
          params[:email_schedule][:dynamic_filter_presets] = dynamic_filter_presets_param
          request.headers['Content-Type'] = 'application/json'
        end

        context 'preset id is not included' do
          let(:dynamic_filter_presets_param) do
            [
              { dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [2], modifier: nil } },
            ]
          end
          it 'removes old preset and creates new one' do
            preset_count = DynamicFilterPreset.count

            put :update, params: params, format: :json
            assert_success_response!

            expect(DynamicFilterPreset.count).to eq preset_count
            expect(DynamicFilterPreset.find_by(id: dynamic_filter_preset.id)).to eq nil

            new_preset = DynamicFilterPreset.last

            expect(new_preset.preset_condition.values).to eq [2]
          end
        end

        context 'preset id is included' do
          let(:dynamic_filter_presets_param) do
            [
              { id: dynamic_filter_preset.id, dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [4], modifier: nil } },
            ]
          end
          it 'updates the existing preset' do
            preset_count = DynamicFilterPreset.count

            put :update, params: params, format: :json
            assert_success_response!

            expect(DynamicFilterPreset.count).to eq preset_count
            expect(dynamic_filter_preset.reload.preset_condition.values).to eq [4]
          end

          context 'changing dynamic_filter_id in params' do
            let!(:dynamic_filter2) do
              FactoryBot.create :dynamic_filter, dynamic_filter_holdable: es.source, order: 1
            end
            before do
              dynamic_filter_presets_param.first[:dynamic_filter_id] = dynamic_filter2.id
            end

            it 'raises error' do
              put :update, params: params, format: :json
              expect(response.status).to eq 422
              expect(response.body).to match(/cannot change/i)
            end
          end

          context 'submitted preset ID actually belongs to another email schedule' do
            let(:es2) do
              FactoryBot.create :email_schedule
            end
            let!(:dynamic_filter_preset2) do
              FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es2, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: [1], modifier: nil }
            end
            before do
              dynamic_filter_presets_param.first[:id] = dynamic_filter_preset2.id
            end

            it 'raises error' do
              put :update, params: params, format: :json
              expect(response.status).to eq 422
              expect(response.body).to match(/does not belong/)
            end
          end
        end

        context 'one with existing ID and one without ID' do
          include_context 'simple_query_model_dataset'
          let(:query_model_sql) do
            <<~SQL
              with t(name,dob) as (
                values('alice', '2022-01-01'),
                  ('bob', '2022-02-01')
              )
              select name, dob::date from t
            SQL
          end
          let(:data_set) { query_model_data_set }
          let!(:dynamic_filter2) do
            filter_source = FactoryBot.create :dm_field_filter_source, data_set_id: data_set.id, field_path: { model_id: query_data_model.id, field_name: 'dob' }
            definition = FactoryBot.create :dynamic_filter_definition, filter_source: filter_source
            FactoryBot.create :dynamic_filter, dynamic_filter_holdable: es.source, order: 1, definition: definition
          end
          let(:dynamic_filter_presets_param) do
            [
              { dynamic_filter_id: dynamic_filter2.id, preset_condition: { operator: 'last', values: [2], modifier: 'day', options: { include_current_period: true } } },
              { id: dynamic_filter_preset.id, dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [8], modifier: nil } },
            ]
          end

          it 'updates existing one and creates new one' do
            preset_count = DynamicFilterPreset.count

            put :update, params: params, format: :json
            assert_success_response!

            expect(DynamicFilterPreset.count).to eq preset_count + 1
            expect(dynamic_filter_preset.reload.preset_condition.values).to eq [8]
            expect(DynamicFilterPreset.last.preset_condition.operator).to eq('last')
            expect(DynamicFilterPreset.last.preset_condition.values).to eq [2]
            expect(DynamicFilterPreset.last.preset_condition.options.to_h).to eq({ include_current_period: true })
            expect(DynamicFilterPreset.last.preset_condition.modifier).to eq('day')
          end
        end

        context 'multiple presets with same ID submitted' do
          let(:dynamic_filter_presets_param) do
            [
              { id: dynamic_filter_preset.id, dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [8], modifier: nil } },
              { id: dynamic_filter_preset.id, dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: [16], modifier: nil } },
            ]
          end

          it 'updates the preset according to the last preset hash' do
            preset_count = DynamicFilterPreset.count
            put :update, params: params, format: :json
            expect(DynamicFilterPreset.count).to eq preset_count
            dynamic_filter_preset.reload
            expect(dynamic_filter_preset.preset_condition.values).to eq [16]
          end
        end
      end
    end

    context('.slack_dest') do
      let(:slack_dest) do
        {
          slack_team_id: 'should not be updated',
          title: 'stellar coin',
          message: 'dont buy TRX',
          slack_channels: [
            { id: 'C112', name: 'Buy Ethereum', type: 'private' },
            { id: 'C@@', name: 'Empty', type: 'public' },
          ],
        }
      end

      before do
        add_slack_team_to_tenant
        dest = FactoryBot.create :slack_dest
        es.dest = dest
        es.save!
      end

      it 'should update data schedule with slack des' do
        hash = JSON.parse(es.to_json).recursive_symbolize_keys
        hash[:dest] = slack_dest
        post :update, params: { id: es.id, email_schedule: hash }

        expect(response.status).to be 200
        es = EmailSchedule.find_by({ source_type: 'QueryReport', dest_type: 'SlackDest'})
        expect(es.dest.slack_team_id).not_to eql(slack_dest[:slack_team_id])
        expect(es.dest.slack_channels).to eql(slack_dest[:slack_channels])
        expect(es.dest.message).to eql(slack_dest[:message])
      end

      it 'should not update with empty channels' do
        hash = JSON.parse(es.to_json).recursive_symbolize_keys
        slack_dest[:slack_channels] = []
        hash[:dest] = slack_dest
        post :update, params: { id: es.id, email_schedule: hash }

        expect(response.status).to be 200
        es = EmailSchedule.find_by({ source_type: 'QueryReport', dest_type: 'SlackDest' })
        expect(es.dest.slack_channels.size).not_to eq 0
      end
    end

    context('gsheet_dest') do
      before do
        create :google_authentication_export, action: GoogleAuthentication::EXPORT_SPREADSHEET.value, owner: admin
        dest = FactoryBot.create :gsheet_dest
        es.dest = dest
        es.save!
      end

      it 'should be able to update gsheet schedule' do
        hash = JSON.parse(es.to_json).recursive_symbolize_keys
        hash[:dest] = gsheet_params[:email_schedule][:dest]
        VCR.use_cassette('google/google_sheet_get') do
          post :update, params: { id: es.id, email_schedule: hash }
        end
        expect(response.status).to eql 200
      end
    end
  end

  describe '.destroy' do
    it 'should destroy email schedule and its dest' do
      post :destroy, params: { id: es.id }
      expect(EmailSchedule.exists?(es.id)).to eq false
      expect(EmailDest.exists?(email_dest.id)).to eq false
    end
  end

  describe '.execute' do
    it 'submits job and return the job id' do
      r = FactoryBot.create :query_report
      es = FactoryBot.create :email_schedule, source: r, creator: admin, tenant: admin.tenant
      ed = FactoryBot.create :email_dest, recipients: %w[<EMAIL> <EMAIL>]
      es.dest = ed
      es.save!

      post :execute, params: { id: es.id }
      expect(response).to be_successful
      expect(JSON.parse(response.body)['job_id']).to be

      # make sure they send the email properly
      mail = ActionMailer::Base.deliveries.last
      expect(mail.to).to eq ['<EMAIL>', '<EMAIL>']
    end

    context 'enabled jobs:sidekiq_schedule_queue FT' do
      before do
        FeatureToggle.toggle_global(Job::FT_SIDEKIQ_SCHEDULE_QUEUE, 'enabled')
      end

      it 'job should go to sidekiq queue schedule' do
        r = FactoryBot.create :query_report
        es = FactoryBot.create :email_schedule, source: r, creator: admin, tenant: admin.tenant
        ed = FactoryBot.create :email_dest, recipients: %w[<EMAIL> <EMAIL>]
        es.dest = ed
        es.save!

        post :execute, params: { id: es.id }
        job = assert_async_response!
        expect(job.data[:worker_options][:queue]).to eq 'schedule'
      end
    end

    context 'when the job fails' do
      let(:failure_recipients) do
        '<EMAIL>'
      end

      it 'sends failure email to report owner' do
        t = get_test_tenant
        analyst = users(:analyst)

        r = FactoryBot.create :query_report, query: 'select wrong value', owner: admin, tenant: t

        # add tenant failure recipients settings
        t.settings[:email] = { failure_recipients: failure_recipients }
        t.save

        es = FactoryBot.create :email_schedule, source: r, creator: analyst, tenant: t
        es.update!(dest: email_dest)

        post :execute, params: { id: es.id }

        expect(response).to be_successful

        # report's owner and schedule's owner should receive an email notif
        mail = ActionMailer::Base.deliveries.first
        expect(mail).to be
        expect(mail.subject).to start_with("Email Schedule Failure (ID: #{es.id}):")
        expect(mail.to).to match_array [failure_recipients, analyst.email, admin.email]
      end

      it 'send failure email to dashboard owner' do
        analyst = users(:analyst)
        admin.share(analyst, :read, error_report.data_source)
        FactoryBot.create :dashboard_widget, dashboard: dashboard, tenant: dashboard.tenant,
                                              source: error_report, title: 'Failed Widget', grid: { row: 2, col: 2 }

        error_es = FactoryBot.create :email_schedule, source: dashboard, creator: analyst, tenant: get_test_tenant
        error_es.update!(dest: email_dest)

        post :execute, params: { id: error_es.id }

        mail = ActionMailer::Base.deliveries.first
        expect(mail).to be
        expect(mail.subject).to start_with("Email Schedule Failure (ID: #{error_es.id}):")
        expect(mail.to_s).to include('Reports/Metrics Failed')
        expect(mail.to).to match_array [analyst.email]
      end
    end
  end

  describe '.test_execute' do
    before do
      create :feature_toggle, key: 'data_schedules:test_execution', toggle_mode: 'enabled'
    end

    it 'returns the job id while asynchronously executes then destroys the data schedule' do
      es_count = EmailSchedule.count
      ed_count = EmailDest.count
      schedule_count = Schedule.count
      fv_count = FilterValue.count

      post :test_execute, params: es_params
      expect(response).to be_successful
      expect(JSON.parse(response.body)['job_id']).to be

      # make sure they send the email properly
      mail = ActionMailer::Base.deliveries.last
      expect(mail.to).to eq ['<EMAIL>']

      # make sure no new data schedule is saved
      expect(EmailSchedule.count).to eq es_count
      expect(EmailDest.count).to eq ed_count
      expect(Schedule.count).to eq schedule_count
      expect(FilterValue.count).to eq fv_count
    end

    it 'can send failure email' do
      t = get_test_tenant

      r = FactoryBot.create :query_report, query: 'select wrong value', owner: admin, tenant: t

      # add tenant failure recipients settings
      failure_recipients = '<EMAIL>'
      t.settings[:email] = { failure_recipients: failure_recipients }
      t.save

      params = es_params.clone
      params[:email_schedule] = params[:email_schedule].merge(
        source_id: r.id,
        source_type: 'QueryReport',
      )

      post :test_execute, params: params

      expect(response).to be_successful

      # report's owner and schedule's owner should receive an email notif
      mail = ActionMailer::Base.deliveries.first
      expect(mail).to be
      expect(mail.subject).to start_with('Email Schedule Failure')
      expect(mail.to).to match_array [failure_recipients, admin.email]

      # shows revamped job logs statuses
      expect(mail.html_part.body.decoded).to include('Status: Pending')
      expect(mail.html_part.body.decoded).to include('Status: Starting')

      expect(EmailSchedule.where(test_only: true).count).to eq 1
    end

    describe 'set necessary data for testing from original data schedule' do
      context 'email dest' do
        let(:password) { '1234' }

        before do
          FeatureToggle.toggle_global('email_schedule:attachment_password', true)
        end
        it 'sets password from original data schedule' do
          dest = FactoryBot.create :email_dest, options: { password_enabled: true, password: password, format: 'excel' }
          es.dest = dest
          es.save!

          params = es_params.clone
          params[:email_schedule] = params[:email_schedule].merge(
            source_id: es.source_id,
            source_type: es.source_type,
            dest: {
              recipients: es.dest.recipients,
              options: { password_enabled: true, password: false, format: 'excel' },
            },
          )
          params[:original_ds_id] = es.id

          post :test_execute, params: params

          mail = ActionMailer::Base.deliveries.last

          expect(mail.to).to eq ['<EMAIL>', '<EMAIL>']

          encrypted_file = mail.attachments.first
          encrypted_filename = encrypted_file.filename
          expect(encrypted_filename).to match(/\.zip$/)

          extracted_file = test_encrypted_excel! encrypted_file.body.raw_source, password
          expect(extracted_file).to match(/\.xlsx$/)
        end
      end

      context 'with dynamic_filter_presets' do
        include_context 'query_model_based_report'
        let(:qr) { query_model_based_report }
        let!(:report_widget) { FactoryBot.create :dashboard_widget, source: qr, dashboard: dashboard }
        let!(:dynamic_filter) do
          FactoryBot.create :dynamic_filter, dynamic_filter_holdable: dashboard, order: 0
        end
        let!(:dynamic_filter_preset) do
          FactoryBot.create :dynamic_filter_preset, dynamic_filter_presettable: es, dynamic_filter: dynamic_filter, preset_condition: { operator: 'is', values: ['bob'], modifier: nil }
        end
        let(:dynamic_filter_presets_param) do
          [
            { id: dynamic_filter_preset.id, dynamic_filter_id: dynamic_filter.id, preset_condition: { operator: 'is', values: ['bob'], modifier: nil } },
          ]
        end
        let(:name_field_path) do
          DataModeling::Values::FieldPath.new(field_name: 'name')
        end
        let!(:widget_name_filter_mapping) do
          FactoryBot.create :dynamic_filter_mapping, dynamic_filter: dynamic_filter, viz_conditionable: report_widget, field_path: name_field_path
        end
        before do
          es_params[:email_schedule][:dynamic_filter_presets] = dynamic_filter_presets_param
          es_params[:email_schedule][:dest][:options] = {
            include_filters: true,
          }
          request.headers['Content-Type'] = 'application/json'
        end
        it 'runs successfully' do
          post :test_execute, params: es_params, format: :json
          assert_success_response!
          mail = ActionMailer::Base.deliveries.last
          mail_text_body = mail.text_part.body.decoded
          expect(mail_text_body.scan(/- Text Filter:\nis "bob"/).count).to eq 1
        end
      end
    end
  end

  describe '#get_source' do
    it 'returns json object for query report' do
      r = FactoryBot.create :query_report
      get :get_source, params: { source_id: r.id, source_type: 'QueryReport' }
      expect(JSON.parse(response.body)['id']).to eq r.id
    end

    it 'returns json object for dashboard' do
      d = FactoryBot.create :dashboard
      get :get_source, params: { source_id: d.id, source_type: 'Dashboard' }
      expect(JSON.parse(response.body)['id']).to eq d.id
    end

    it 'returns json object for metric sheet' do
      ms = FactoryBot.create :metric_sheet
      get :get_source, params: { source_id: ms.id, source_type: 'MetricSheet' }

      json_body = JSON.parse(response.body)

      expect(json_body['id']).to eq ms.id

      # permissions atrrbutes are present
      expect(json_body['can_share']).to be_nil
      expect(json_body['can_update']).to be_nil
    end

    context 'when dashboard has many dashboard widget' do
      it 'does not have N+1 issues' do
        db = create :dashboard
        qr = create :query_report
        qr2 = create :query_report
        qm = create :query_metric
        qm2 = create :query_metric
        create(:dashboard_widget, dashboard: db, source: qr)
        create(:dashboard_widget, dashboard: db, source: qr2)
        create(:dashboard_widget, dashboard: db, source: qm)
        create(:dashboard_widget, dashboard: db, source: qm2)
        DashboardWidget.create(source_type: 'Text', dashboard: db)
        DashboardWidget.create(source_type: 'Text', dashboard: db)

        Prosopite.scan do
          get :get_source, format: :json, params: { source_id: db.id, source_type: 'Dashboard' }
        end
      end
    end
  end

  describe '#recipients' do
    it 'returns recipients for admin' do
      get :recipients
      expect(JSON.parse(response.body)['recipients']).to eq email_dest.recipients
    end

    it 'response with 403 for analyst' do
      sign_in get_test_analyst
      get :recipients
      expect(response).to have_http_status(:forbidden)
      expect(response.body).not_to include(email_dest.recipients[0])
    end
  end

  describe '#delete_recipient' do
    it 'delete recipient for admin' do
      delete :delete_recipient, params: { recipient: email_dest.recipients[0] }
      expect(response).to have_http_status(:ok)
      expect(email_dest.reload.recipients.size).to eq(1)
    end

    it 'response with 403 for analyst' do
      sign_in get_test_analyst
      delete :delete_recipient, params: { recipient: email_dest.recipients[0] }
      expect(response).to have_http_status(:forbidden)
      expect(email_dest.reload.recipients.size).to eq(2)
    end
  end

  describe '#parse_dynamic_string' do
    it 'parse given string' do
      params = {
        email_schedule: { source_id: query_report.id, source_type: 'QueryReport' },
        str: '{{$source_title}}/{{$today}}',
      }

      date_str = Time.now.strftime('%Y-%m-%d')
      Timecop.freeze(DateTime.parse("#{date_str} 21:30:00 UTC")) do
        post :parse_dynamic_string, params: params
        expect(JSON.parse(response.body)['parsed_string']).to eq "random_report/#{date_str}"
      end

      analyst = get_test_analyst
      sign_in analyst
      @controller = described_class.new

      Timecop.freeze(DateTime.parse("#{date_str} 21:30:00 UTC")) do
        post :parse_dynamic_string, params: params
        expect(response).to have_http_status(:forbidden)

        admin.share(analyst, :read, query_report)
        @controller = described_class.new

        post :parse_dynamic_string, params: params
        expect(response).to have_http_status(:ok)
        expect(JSON.parse(response.body)['parsed_string']).to eq "random_report/#{date_str}"
      end

      @controller = nil
    end

    context 'sftp dest + dashboard source' do
      let(:sftp_data_source) { create :test_ds_sftp }
      let(:widget) { create :dashboard_widget, dashboard: dashboard, source: query_report }
      let!(:sftp_dest) { create :sftp_dest, data_source_id: sftp_data_source.id, tenant_id: sftp_data_source.tenant_id, dashboard_widget_id: widget.id }
      let!(:sftp_ds) do
        create :email_schedule, tenant_id: get_test_tenant.id, creator_id: admin.id, dest_type: 'SftpDest', dest_id: sftp_dest.id
      end

      it 'no widget id provided' do
        params = {
          email_schedule: { source_id: dashboard.id, source_type: 'Dashboard', dest_type: 'SftpDest' },
          str: '{{$source_title}}',
        }
        post :parse_dynamic_string, params: params
        assert_success_response!
        expect(JSON.parse(response.body)['parsed_string']).to eq 'widget_not_selected'
      end

      it 'parse widget title' do
        params = {
          email_schedule: { source_id: dashboard.id, source_type: 'Dashboard', dest_type: 'SftpDest', dest: { dashboard_widget_id: widget.id } },
          str: '{{$source_title}}',
        }
        post :parse_dynamic_string, params: params
        assert_success_response!
        expect(JSON.parse(response.body)['parsed_string']).to eq widget.title.to_s.parameterize(separator: '_')
      end

      it 'parse report title' do
        widget.update(title: '')
        params = {
          email_schedule: { source_id: dashboard.id, source_type: 'Dashboard', dest_type: 'SftpDest', dest: { dashboard_widget_id: widget.id } },
          str: '{{$source_title}}',
        }
        post :parse_dynamic_string, params: params
        assert_success_response!
        expect(JSON.parse(response.body)['parsed_string']).to eq query_report.title.to_s.parameterize(separator: '_')
      end
    end
  end

  describe 'track the activities' do
    let(:filter_values) do
      [
        {
          'type' => 'date',
          'name' => 'date',
          'label' => 'Date',
          'filter_ownership_id' => '48',
          'selected_value' => nil,
          'settings' => { 'read_only' => 'true', 'override' => 'false'},
          'possibly_outdated' => 'false',
        },
        {
          'type' => 'date_range',
          'name' => 'date_range',
          'label' => 'Date Range',
          'filter_ownership_id' => '49',
          'selected_value' => nil,
          'settings' => {'read_only' => 'true', 'override' => 'false'},
          'selected_value_start' => '',
          'selected_value_end' => '',
          'possibly_outdated' => 'false',
        },
      ]
    end

    let(:new_es) do
      params = es_params.dup
      params[:email_schedule][:filter_values] = filter_values

      post :create, params: params
      EmailSchedule.last
    end

    before do
      expect(ActivityLog.where(key: 'email_schedule.create').count).to eq 0
      new_es
    end

    it 'create' do
      expect(ActivityLog.where(key: 'email_schedule.create').count).to eq 1
      dest = {
        'options' => { 'base_url' => 'http://test.host' },
        'title' => '123',
        'recipients' => ['<EMAIL>'],
      }
      log = ActivityLog.last
      expect(log.parameters['filter_values']).to eq filter_values
      expect(log.parameters['dest']).to eq dest
    end

    it 'create with title field in activity log' do
      params_with_title = es_params.deep_dup
      params_with_title[:email_schedule][:title] = 'Activity Log Title'
      params_with_title[:email_schedule][:filter_values] = filter_values

      post :create, params: params_with_title
      expect(response).to be_successful

      expect(ActivityLog.where(key: 'email_schedule.create').count).to eq 2
      log = ActivityLog.last
      expect(log.parameters['title']).to eq 'Activity Log Title'
      expect(log.parameters['filter_values']).to eq filter_values
    end

    it 'update' do
      expect(ActivityLog.where(key: 'email_schedule.update').count).to eq 0

      updated_es_params = es_params.merge(id: new_es.id)
      updated_es_params[:email_schedule][:filter_values] = [filter_values.first]
      updated_es_params[:email_schedule][:id] = new_es.id
      post :update, params: updated_es_params

      expect(ActivityLog.where(key: 'email_schedule.update').count).to eq 1
      log = ActivityLog.last
      expect(log.parameters['filter_values']).to eq [filter_values.first]
    end

    it 'update with title field in activity log' do
      expect(ActivityLog.where(key: 'email_schedule.update').count).to eq 0

      updated_es_params = es_params.merge(id: new_es.id)
      updated_es_params[:email_schedule][:filter_values] = [filter_values.first]
      updated_es_params[:email_schedule][:id] = new_es.id
      updated_es_params[:email_schedule][:title] = 'Updated Activity Log Title'
      post :update, params: updated_es_params

      expect(ActivityLog.where(key: 'email_schedule.update').count).to eq 1
      log = ActivityLog.last
      expect(log.parameters['title']).to eq 'Updated Activity Log Title'
      expect(log.parameters['filter_values']).to eq [filter_values.first]
    end

    it 'destroy' do
      expect(ActivityLog.where(key: 'email_schedule.destroy').count).to eq 0
      post :destroy, params: { id: new_es.id }
      expect(ActivityLog.where(key: 'email_schedule.destroy').count).to eq 1
    end
  end
end
